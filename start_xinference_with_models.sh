#!/bin/bash

# Xinference 启动脚本，包含模型自动加载

set -e

# 激活虚拟环境
source /root/anaconda3/etc/profile.d/conda.sh
conda activate xinference

# 启动 xinference 服务器
echo "启动 Xinference 服务器..."
xinference-local --host 0.0.0.0 --port 9997 &
XINFERENCE_PID=$!

# 等待服务器启动
echo "等待服务器启动..."
sleep 30

# 检查服务器是否启动成功
if ! curl -s http://localhost:9997/v1/models > /dev/null; then
    echo "Xinference 服务器启动失败"
    kill $XINFERENCE_PID 2>/dev/null || true
    exit 1
fi

echo "Xinference 服务器启动成功，开始加载模型..."

# 启动 Embedding 模型
echo "启动 bge-m3 embedding 模型..."
xinference launch --model-name bge-m3 --model-type embedding &

# 启动 Rerank 模型
echo "启动 bge-reranker-v2-m3 rerank 模型..."
xinference launch --model-name bge-reranker-v2-m3 --model-type rerank --replica 1 --n-gpu auto --download_hub modelscope --model-engine vllm --model-format pytorch --quantization none &

# 启动 LLM 模型
echo "启动 qwen3-8B LLM 模型..."
xinference launch --model-name qwen3 --model-type LLM --model-engine Transformers --model-format pytorch --size-in-billions 8 --quantization none --n-gpu auto --replica 2 --n-worker 1 --download_hub modelscope --enable_thinking false &

echo "所有模型启动命令已发送，模型将在后台加载..."
echo "Xinference 服务器 PID: $XINFERENCE_PID"

# 保持脚本运行，等待主进程
wait $XINFERENCE_PID
