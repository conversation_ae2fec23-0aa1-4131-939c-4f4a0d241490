[Unit]
Description=Xinference Local Server with Auto Model Loading
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root
ExecStart=/bin/bash /root/start_xinference_with_models.sh
Restart=always
RestartSec=30
StandardOutput=append:journal
StandardError=append:journal
Environment=PATH=/root/anaconda3/envs/xinference/bin:/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
Environment=CONDA_DEFAULT_ENV=xinference
Environment=CONDA_PREFIX=/root/anaconda3/envs/xinference

# 给足够的时间启动
TimeoutStartSec=600

[Install]
WantedBy=multi-user.target
