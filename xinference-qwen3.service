[Unit]
Description=Xinference Qwen3-8B LLM Model
After=network.target xinference.service
Wants=network-online.target
Requires=xinference.service

[Service]
Type=oneshot
User=root
Group=root
WorkingDirectory=/root
ExecStart=/bin/bash -c 'source /root/anaconda3/etc/profile.d/conda.sh && conda activate xinference && sleep 120 && xinference launch --model-name qwen3 --model-type LLM --model-engine Transformers --model-format pytorch --size-in-billions 8 --quantization none --n-gpu auto --replica 2 --n-worker 1 --download_hub modelscope --enable_thinking false'
RemainAfterExit=yes
StandardOutput=append:journal
StandardError=append:journal
Environment=PATH=/root/anaconda3/envs/xinference/bin:/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
TimeoutStartSec=900

[Install]
WantedBy=multi-user.target
