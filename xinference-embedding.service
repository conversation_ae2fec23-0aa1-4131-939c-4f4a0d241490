[Unit]
Description=Xinference BGE-M3 Embedding Model
After=network.target xinference.service
Wants=network-online.target
Requires=xinference.service

[Service]
Type=oneshot
User=root
Group=root
WorkingDirectory=/root
ExecStart=/bin/bash -c 'source /root/anaconda3/etc/profile.d/conda.sh && conda activate xinference && sleep 60 && xinference launch --model-name bge-m3 --model-type embedding'
RemainAfterExit=yes
StandardOutput=append:journal
StandardError=append:journal
Environment=PATH=/root/anaconda3/envs/xinference/bin:/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
TimeoutStartSec=300

[Install]
WantedBy=multi-user.target
