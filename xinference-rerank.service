[Unit]
Description=Xinference BGE-Reranker-V2-M3 Rerank Model
After=network.target xinference.service
Wants=network-online.target
Requires=xinference.service

[Service]
Type=oneshot
User=root
Group=root
WorkingDirectory=/root
ExecStart=/bin/bash -c 'source /root/anaconda3/etc/profile.d/conda.sh && conda activate xinference && sleep 90 && xinference launch --model-name bge-reranker-v2-m3 --model-type rerank --replica 1 --n-gpu auto --download_hub modelscope --model-engine vllm --model-format pytorch --quantization none'
RemainAfterExit=yes
StandardOutput=append:journal
StandardError=append:journal
Environment=PATH=/root/anaconda3/envs/xinference/bin:/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
TimeoutStartSec=600

[Install]
WantedBy=multi-user.target
