[Unit]
Description=Xinference Local Server with Models
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root
ExecStart=/bin/bash -c 'source /root/anaconda3/etc/profile.d/conda.sh && conda activate xinference && xinference-local --host 0.0.0.0 --port 9997 & sleep 30 && xinference launch --model-name bge-m3 --model-type embedding & xinference launch --model-name bge-reranker-v2-m3 --model-type rerank --replica 1 --n-gpu auto --download_hub modelscope --model-engine vllm --model-format pytorch --quantization none & xinference launch --model-name qwen3 --model-type LLM --model-engine Transformers --model-format pytorch --size-in-billions 8 --quantization none --n-gpu auto --replica 2 --n-worker 1 --download_hub modelscope --enable_thinking false & wait'
Restart=always
RestartSec=30
StandardOutput=append:journal
StandardError=append:journal
Environment=PATH=/root/anaconda3/envs/xinference/bin:/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
TimeoutStartSec=600

[Install]
WantedBy=multi-user.target
