#!/bin/bash

# Xinference Systemd Service 安装脚本

set -e

echo "正在安装 Xinference systemd 服务..."

# 检查虚拟环境是否存在
if [ ! -f "/root/anaconda3/envs/xinference/bin/xinference-local" ]; then
    echo "警告: 虚拟环境中的 xinference-local 不存在"
    echo "请确认路径: /root/anaconda3/envs/xinference/bin/xinference-local"
    echo "或修改 xinference.service 文件中的 ExecStart 路径"
fi

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请使用 sudo 运行此脚本"
    exit 1
fi

# 复制服务文件到 systemd 目录
echo "复制服务文件..."
cp xinference.service /etc/systemd/system/

# 重新加载 systemd
echo "重新加载 systemd..."
systemctl daemon-reload

# 启用服务（开机自启）
echo "启用服务..."
systemctl enable xinference.service

# 启动服务
echo "启动服务..."
systemctl start xinference.service

# 检查服务状态
echo "检查服务状态..."
systemctl status xinference.service --no-pager

echo ""
echo "安装完成！"
echo ""
echo "常用命令："
echo "  查看状态: sudo systemctl status xinference"
echo "  启动服务: sudo systemctl start xinference"
echo "  停止服务: sudo systemctl stop xinference"
echo "  重启服务: sudo systemctl restart xinference"
echo "  查看日志: sudo journalctl -u xinference -f"
echo "  禁用开机自启: sudo systemctl disable xinference"
echo ""
echo "日志文件位置: /opt/opentelmetry/xinference.log"
