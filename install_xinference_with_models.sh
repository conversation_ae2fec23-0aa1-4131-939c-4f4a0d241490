#!/bin/bash

# 安装 Xinference 带模型自动加载的 systemd 服务

set -e

echo "正在安装 Xinference 带模型自动加载的 systemd 服务..."

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请使用 sudo 运行此脚本"
    exit 1
fi

# 复制启动脚本到 /root
echo "复制启动脚本..."
cp start_xinference_with_models.sh /root/
chmod +x /root/start_xinference_with_models.sh

# 复制服务文件到 systemd 目录
echo "复制服务文件..."
cp xinference-with-models.service /etc/systemd/system/

# 重新加载 systemd
echo "重新加载 systemd..."
systemctl daemon-reload

# 启用服务（开机自启）
echo "启用服务..."
systemctl enable xinference-with-models.service

echo ""
echo "安装完成！"
echo ""
echo "启动服务: sudo systemctl start xinference-with-models"
echo "查看状态: sudo systemctl status xinference-with-models"
echo "查看日志: sudo journalctl -u xinference-with-models -f"
echo "停止服务: sudo systemctl stop xinference-with-models"
echo ""
echo "注意: 首次启动可能需要较长时间下载模型，请耐心等待"
