[Unit]
Description=Xinference Local Server
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=
ExecStart=/root/anaconda3/envs/xinference/bin/xinference-local --host 0.0.0.0 --port 9997
Environment=PATH=/root/anaconda3/envs/xinference/bin:/usr/local/bin:/usr/bin:/bin
Restart=always
RestartSec=10
StandardOutput=append:journal
StandardError=append:journal


[Install]
WantedBy=multi-user.target
