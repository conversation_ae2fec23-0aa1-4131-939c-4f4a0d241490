[Unit]
Description=Xinference Local Server
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=your_username
Group=your_group
WorkingDirectory=/opt/opentelmetry
ExecStart=/usr/local/bin/xinference-local --host 0.0.0.0 --port 9997
Restart=always
RestartSec=10
StandardOutput=append:/opt/opentelmetry/xinference.log
StandardError=append:/opt/opentelmetry/xinference.log

# 环境变量（如果需要的话）
Environment=PATH=/usr/local/bin:/usr/bin:/bin
# Environment=PYTHONPATH=/path/to/your/python/modules

# 资源限制（可选）
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
